"""Unit tests for web crawler implementation."""

import pytest
import asyncio
import aiohttp
from unittest.mock import Mock, AsyncMock, patch, mock_open
from pathlib import Path

from src.offline_pipeline.crawling.crawler import <PERSON>Crawler, DocumentDownloader
from src.shared.models import DataSource, SourceType


class TestWebCrawler:
    """Test cases for WebCrawler."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch('src.offline_pipeline.crawling.crawler.get_settings') as mock:
            mock.return_value.crawled_data_dir = "/tmp/test_crawled"
            yield mock.return_value
    
    @pytest.fixture
    def crawler(self, mock_settings):
        """Create crawler instance for testing."""
        with patch('src.offline_pipeline.crawling.crawler.ensure_directory_exists'):
            return WebCrawler()
    
    @pytest.fixture
    def sample_source(self):
        """Sample data source for testing."""
        return DataSource(
            id="test_source",
            name="Test Website",
            source_type=SourceType.WEBSITE,
            url="https://example.com",
            crawl_depth=2,
            enabled=True
        )
    
    async def test_crawler_initialization(self, crawler):
        """Test crawler initialization."""
        assert crawler.delay_between_requests == 1.0
        assert crawler.max_concurrent_requests == 5
        assert crawler.timeout == 30
        assert crawler.user_agent == "RAG-Legal-Chatbot/1.0"
        assert crawler.respect_robots_txt is True
        assert crawler.session is None
        assert len(crawler.visited_urls) == 0
    
    async def test_create_session(self, crawler):
        """Test session creation."""
        await crawler._create_session()
        
        assert crawler.session is not None
        assert isinstance(crawler.session, aiohttp.ClientSession)
        
        # Clean up
        await crawler._close_session()
    
    async def test_close_session(self, crawler):
        """Test session cleanup."""
        await crawler._create_session()
        session = crawler.session
        
        await crawler._close_session()
        
        assert session.closed
    
    async def test_context_manager(self, crawler):
        """Test async context manager."""
        async with crawler as c:
            assert c.session is not None
            assert isinstance(c.session, aiohttp.ClientSession)
        
        # Session should be closed after context
        assert crawler.session.closed
    
    @patch('src.offline_pipeline.crawling.crawler.RobotFileParser')
    async def test_can_fetch_robots_allowed(self, mock_robot_parser, crawler):
        """Test robots.txt check when crawling is allowed."""
        # Mock session
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = "User-agent: *\nAllow: /"
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        crawler.session = mock_session
        
        # Mock robot parser
        mock_parser = Mock()
        mock_parser.can_fetch.return_value = True
        mock_robot_parser.return_value = mock_parser
        
        result = await crawler._can_fetch("https://example.com/page")
        
        assert result is True
        mock_session.get.assert_called_once()
        mock_parser.can_fetch.assert_called_once()
    
    @patch('src.offline_pipeline.crawling.crawler.RobotFileParser')
    async def test_can_fetch_robots_disallowed(self, mock_robot_parser, crawler):
        """Test robots.txt check when crawling is disallowed."""
        # Mock session
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.text.return_value = "User-agent: *\nDisallow: /"
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        crawler.session = mock_session
        
        # Mock robot parser
        mock_parser = Mock()
        mock_parser.can_fetch.return_value = False
        mock_robot_parser.return_value = mock_parser
        
        result = await crawler._can_fetch("https://example.com/page")
        
        assert result is False
    
    async def test_can_fetch_no_robots_txt(self, crawler):
        """Test robots.txt check when robots.txt doesn't exist."""
        # Mock session
        mock_response = AsyncMock()
        mock_response.status = 404
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        crawler.session = mock_session
        
        result = await crawler._can_fetch("https://example.com/page")
        
        assert result is True  # Should allow when robots.txt doesn't exist
    
    def test_generate_filename(self, crawler):
        """Test filename generation from URLs."""
        # Test normal path
        filename = crawler._generate_filename("https://example.com/legal/contracts", 1)
        assert filename == "depth_1_legal_contracts.html"
        
        # Test root path
        filename = crawler._generate_filename("https://example.com/", 0)
        assert filename == "depth_0_index.html"
        
        # Test path with special characters
        filename = crawler._generate_filename("https://example.com/legal-docs/§123", 2)
        assert filename == "depth_2_legal-docs__123.html"
    
    def test_extract_links(self, crawler):
        """Test link extraction from HTML."""
        html_content = """
        <html>
        <body>
            <a href="/page1">Page 1</a>
            <a href="https://example.com/page2">Page 2</a>
            <a href="../page3">Page 3</a>
            <div>No link here</div>
        </body>
        </html>
        """
        
        links = crawler._extract_links(html_content, "https://example.com/base/")
        
        assert len(links) == 3
        assert "https://example.com/page1" in links
        assert "https://example.com/page2" in links
        assert "https://example.com/page3" in links
    
    def test_is_valid_link(self, crawler):
        """Test link validation."""
        base_url = "https://example.com"
        
        # Valid same-domain link
        assert crawler._is_valid_link("https://example.com/page", base_url) is True
        
        # Invalid different domain
        assert crawler._is_valid_link("https://other.com/page", base_url) is False
        
        # Invalid file types
        assert crawler._is_valid_link("https://example.com/file.pdf", base_url) is False
        assert crawler._is_valid_link("https://example.com/file.zip", base_url) is False
        
        # Invalid fragments and query params
        assert crawler._is_valid_link("https://example.com/page#section", base_url) is False
        assert crawler._is_valid_link("https://example.com/page?print=1", base_url) is False
    
    @patch('aiofiles.open', new_callable=mock_open)
    @patch('src.offline_pipeline.crawling.crawler.ensure_directory_exists')
    async def test_fetch_and_save_page(self, mock_ensure_dir, mock_aiofiles, crawler):
        """Test fetching and saving a page."""
        # Mock session response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'content-type': 'text/html'}
        mock_response.text.return_value = "<html><body><a href='/link'>Link</a></body></html>"
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        crawler.session = mock_session
        
        # Mock site directory
        site_dir = Path("/tmp/test_site")
        
        result = await crawler._fetch_and_save_page("https://example.com/page", site_dir, 1)
        
        assert result is not None
        file_path, links = result
        assert "depth_1_page.html" in file_path
        assert len(links) == 1
        assert "https://example.com/link" in links
    
    async def test_fetch_and_save_page_non_html(self, crawler):
        """Test fetching non-HTML content."""
        # Mock session response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'content-type': 'application/pdf'}
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        crawler.session = mock_session
        
        site_dir = Path("/tmp/test_site")
        
        result = await crawler._fetch_and_save_page("https://example.com/file.pdf", site_dir, 1)
        
        assert result is None  # Should skip non-HTML content
    
    async def test_fetch_and_save_page_http_error(self, crawler):
        """Test handling HTTP errors."""
        # Mock session response
        mock_response = AsyncMock()
        mock_response.status = 404
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        crawler.session = mock_session
        
        site_dir = Path("/tmp/test_site")
        
        result = await crawler._fetch_and_save_page("https://example.com/notfound", site_dir, 1)
        
        assert result is None  # Should return None for HTTP errors
    
    async def test_crawl_website_robots_disallowed(self, crawler, sample_source):
        """Test crawling when robots.txt disallows."""
        await crawler._create_session()
        
        with patch.object(crawler, '_can_fetch', return_value=False):
            result = await crawler.crawl_website(sample_source)
        
        assert result == []
        
        await crawler._close_session()
    
    async def test_crawl_website_no_url(self, crawler):
        """Test crawling source without URL."""
        source = DataSource(
            name="Test",
            source_type=SourceType.WEBSITE,
            url=None  # No URL
        )
        
        with pytest.raises(ValueError, match="Website source must have a URL"):
            await crawler.crawl_website(source)


class TestDocumentDownloader:
    """Test cases for DocumentDownloader."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch('src.offline_pipeline.crawling.crawler.get_settings') as mock:
            mock.return_value.crawled_data_dir = "/tmp/test_downloads"
            yield mock.return_value
    
    @pytest.fixture
    def downloader(self, mock_settings):
        """Create downloader instance for testing."""
        with patch('src.offline_pipeline.crawling.crawler.ensure_directory_exists'):
            return DocumentDownloader()
    
    async def test_download_pdf_success(self, downloader):
        """Test successful PDF download."""
        # Mock aiohttp session and response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.content.iter_chunked.return_value = [b"PDF content chunk 1", b"PDF content chunk 2"]
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_session):
            with patch('builtins.open', mock_open()) as mock_file:
                result = await downloader.download_pdf("https://example.com/doc.pdf", "test.pdf")
        
        assert "/tmp/test_downloads/test.pdf" in result
        mock_file.assert_called_once()
    
    async def test_download_pdf_http_error(self, downloader):
        """Test PDF download with HTTP error."""
        # Mock aiohttp session and response
        mock_response = AsyncMock()
        mock_response.status = 404
        mock_response.raise_for_status.side_effect = aiohttp.ClientResponseError(
            request_info=Mock(), history=(), status=404
        )
        
        mock_session = AsyncMock()
        mock_session.get.return_value.__aenter__.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_session):
            with pytest.raises(aiohttp.ClientResponseError):
                await downloader.download_pdf("https://example.com/notfound.pdf", "test.pdf")
    
    def test_copy_local_file_success(self, downloader):
        """Test successful local file copy."""
        with patch('pathlib.Path.exists', return_value=True):
            with patch('shutil.copy2') as mock_copy:
                result = downloader.copy_local_file("/source/file.pdf", "target.pdf")
        
        assert "/tmp/test_downloads/target.pdf" in result
        mock_copy.assert_called_once()
    
    def test_copy_local_file_not_found(self, downloader):
        """Test copying non-existent local file."""
        with patch('pathlib.Path.exists', return_value=False):
            with pytest.raises(FileNotFoundError):
                downloader.copy_local_file("/nonexistent/file.pdf", "target.pdf")


@pytest.mark.asyncio
class TestCrawlerIntegration:
    """Integration tests for web crawler."""
    
    @pytest.mark.integration
    async def test_crawl_real_website(self):
        """Test crawling a real website (requires internet)."""
        try:
            crawler = WebCrawler()
            
            # Use a simple test website
            source = DataSource(
                name="HTTPBin Test",
                source_type=SourceType.WEBSITE,
                url="https://httpbin.org/html",
                crawl_depth=1,
                enabled=True
            )
            
            async with crawler:
                result = await crawler.crawl_website(source)
            
            assert isinstance(result, list)
            # Should have at least one file if successful
            if result:
                assert all(isinstance(path, str) for path in result)
                
        except Exception as e:
            pytest.skip(f"Internet connection required for integration test: {e}")
