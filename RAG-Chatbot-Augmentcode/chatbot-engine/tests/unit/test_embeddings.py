"""Unit tests for embedding generation service."""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

from src.offline_pipeline.embeddings import (
    GoogleAIEmbeddingService,
    EmbeddingProcessor,
    create_embedding_processor
)
from src.shared.models import DocumentChunk


class TestGoogleAIEmbeddingService:
    """Test cases for GoogleAIEmbeddingService."""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch('src.offline_pipeline.embeddings.get_settings') as mock:
            mock.return_value.embedding_model = "models/text-embedding-004"
            mock.return_value.google_api_key = "test_api_key"
            mock.return_value.embedding_dimension = 768
            yield mock.return_value
    
    @pytest.fixture
    def embedding_service(self, mock_settings):
        """Create embedding service for testing."""
        with patch('src.offline_pipeline.embeddings.GoogleGenerativeAIEmbeddings') as mock_embeddings:
            service = GoogleAIEmbeddingService()
            service.embeddings = mock_embeddings.return_value
            return service
    
    async def test_generate_single_embedding(self, embedding_service):
        """Test generating a single embedding."""
        # Mock the embedding response
        mock_embedding = [0.1, 0.2, 0.3] + [0.0] * 765
        embedding_service.embeddings.embed_query = Mock(return_value=mock_embedding)
        
        with patch('asyncio.to_thread', return_value=mock_embedding):
            result = await embedding_service.generate_single_embedding("test text")
        
        assert result == mock_embedding
        assert len(result) == 768
    
    async def test_generate_embeddings_batch(self, embedding_service):
        """Test generating embeddings for multiple texts."""
        texts = ["text 1", "text 2", "text 3"]
        mock_embeddings = [
            [0.1] * 768,
            [0.2] * 768,
            [0.3] * 768
        ]
        
        embedding_service.embeddings.embed_documents = Mock(return_value=mock_embeddings)
        
        with patch('asyncio.to_thread', return_value=mock_embeddings):
            results = await embedding_service.generate_embeddings(texts)
        
        assert len(results) == 3
        assert all(len(emb) == 768 for emb in results)
    
    async def test_generate_embeddings_empty_list(self, embedding_service):
        """Test generating embeddings for empty list."""
        result = await embedding_service.generate_embeddings([])
        assert result == []
    
    async def test_generate_embeddings_large_batch(self, embedding_service):
        """Test generating embeddings for large batch (tests batching logic)."""
        # Create 150 texts to test batching (batch_size = 100)
        texts = [f"text {i}" for i in range(150)]
        
        # Mock embeddings for each batch
        batch1_embeddings = [[0.1] * 768 for _ in range(100)]
        batch2_embeddings = [[0.2] * 768 for _ in range(50)]
        
        embedding_service.embeddings.embed_documents = Mock(
            side_effect=[batch1_embeddings, batch2_embeddings]
        )
        
        with patch('asyncio.to_thread', side_effect=[batch1_embeddings, batch2_embeddings]):
            with patch('asyncio.sleep'):  # Mock sleep to speed up test
                results = await embedding_service.generate_embeddings(texts)
        
        assert len(results) == 150
        assert embedding_service.embeddings.embed_documents.call_count == 2
    
    async def test_embed_chunks(self, embedding_service):
        """Test embedding document chunks."""
        chunks = [
            DocumentChunk(
                id="chunk_1",
                content="First chunk content",
                source_id="source_1"
            ),
            DocumentChunk(
                id="chunk_2",
                content="Second chunk content",
                source_id="source_1"
            )
        ]
        
        mock_embeddings = [
            [0.1] * 768,
            [0.2] * 768
        ]
        
        embedding_service.embeddings.embed_documents = Mock(return_value=mock_embeddings)
        
        with patch('asyncio.to_thread', return_value=mock_embeddings):
            result_chunks = await embedding_service.embed_chunks(chunks)
        
        assert len(result_chunks) == 2
        assert result_chunks[0].embedding == [0.1] * 768
        assert result_chunks[1].embedding == [0.2] * 768
    
    async def test_embed_chunks_empty_list(self, embedding_service):
        """Test embedding empty chunk list."""
        result = await embedding_service.embed_chunks([])
        assert result == []


class TestEmbeddingProcessor:
    """Test cases for EmbeddingProcessor."""
    
    @pytest.fixture
    def mock_embedding_service(self):
        """Mock embedding service for testing."""
        service = Mock()
        service.embed_chunks = AsyncMock()
        return service
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing."""
        with patch('src.offline_pipeline.embeddings.get_settings') as mock:
            mock.return_value.embedding_dimension = 768
            yield mock.return_value
    
    @pytest.fixture
    def embedding_processor(self, mock_embedding_service, mock_settings):
        """Create embedding processor for testing."""
        return EmbeddingProcessor(mock_embedding_service)
    
    @pytest.fixture
    def sample_chunks(self):
        """Sample chunks for testing."""
        return [
            DocumentChunk(
                id="chunk_1",
                content="First chunk",
                source_id="source_1"
            ),
            DocumentChunk(
                id="chunk_2",
                content="Second chunk",
                source_id="source_1",
                embedding=[0.1] * 768  # Already has embedding
            )
        ]
    
    async def test_process_chunks_batch(self, embedding_processor, mock_embedding_service, sample_chunks):
        """Test processing a batch of chunks."""
        # Mock the embedding service to return chunks with embeddings
        embedded_chunks = sample_chunks.copy()
        embedded_chunks[0].embedding = [0.2] * 768
        
        mock_embedding_service.embed_chunks.return_value = [embedded_chunks[0]]
        
        result = await embedding_processor.process_chunks_batch(sample_chunks)
        
        assert len(result) == 2
        assert result[0].embedding == [0.2] * 768  # New embedding
        assert result[1].embedding == [0.1] * 768  # Existing embedding
        
        # Should only embed chunks without embeddings
        mock_embedding_service.embed_chunks.assert_called_once()
        call_args = mock_embedding_service.embed_chunks.call_args[0][0]
        assert len(call_args) == 1  # Only one chunk without embedding
        assert call_args[0].id == "chunk_1"
    
    async def test_process_chunks_batch_all_embedded(self, embedding_processor, mock_embedding_service):
        """Test processing chunks that all have embeddings."""
        chunks = [
            DocumentChunk(
                id="chunk_1",
                content="First chunk",
                source_id="source_1",
                embedding=[0.1] * 768
            ),
            DocumentChunk(
                id="chunk_2",
                content="Second chunk",
                source_id="source_1",
                embedding=[0.2] * 768
            )
        ]
        
        result = await embedding_processor.process_chunks_batch(chunks)
        
        assert len(result) == 2
        # Should not call embedding service since all chunks have embeddings
        mock_embedding_service.embed_chunks.assert_not_called()
    
    async def test_process_chunks_stream(self, embedding_processor, mock_embedding_service):
        """Test streaming processing of chunks."""
        # Create 75 chunks to test batching (batch_size = 50)
        chunks = [
            DocumentChunk(
                id=f"chunk_{i}",
                content=f"Content {i}",
                source_id="source_1"
            )
            for i in range(75)
        ]
        
        # Mock embedding service to return chunks with embeddings
        def mock_embed_chunks(chunk_list):
            for chunk in chunk_list:
                chunk.embedding = [0.1] * 768
            return chunk_list
        
        mock_embedding_service.embed_chunks.side_effect = mock_embed_chunks
        
        with patch('asyncio.sleep'):  # Mock sleep to speed up test
            result = await embedding_processor.process_chunks_stream(chunks, batch_size=50)
        
        assert len(result) == 75
        assert all(chunk.embedding == [0.1] * 768 for chunk in result)
        # Should be called twice (50 + 25 chunks)
        assert mock_embedding_service.embed_chunks.call_count == 2
    
    async def test_validate_embeddings(self, embedding_processor):
        """Test embedding validation."""
        chunks = [
            DocumentChunk(
                id="chunk_1",
                content="First chunk",
                source_id="source_1",
                embedding=[0.1] * 768  # Correct dimension
            ),
            DocumentChunk(
                id="chunk_2",
                content="Second chunk",
                source_id="source_1",
                embedding=[0.2] * 512  # Wrong dimension
            ),
            DocumentChunk(
                id="chunk_3",
                content="Third chunk",
                source_id="source_1"
                # No embedding
            )
        ]
        
        validation = await embedding_processor.validate_embeddings(chunks)
        
        assert validation["total_chunks"] == 3
        assert validation["embedded_chunks"] == 2
        assert validation["missing_embeddings"] == 1
        assert validation["correct_dimension_count"] == 1
        assert validation["validation_passed"] is False
    
    async def test_reprocess_failed_embeddings(self, embedding_processor, mock_embedding_service):
        """Test reprocessing failed embeddings."""
        chunks = [
            DocumentChunk(
                id="chunk_1",
                content="First chunk",
                source_id="source_1",
                embedding=[0.1] * 768  # Good embedding
            ),
            DocumentChunk(
                id="chunk_2",
                content="Second chunk",
                source_id="source_1",
                embedding=[0.2] * 512  # Wrong dimension
            ),
            DocumentChunk(
                id="chunk_3",
                content="Third chunk",
                source_id="source_1"
                # No embedding
            )
        ]
        
        # Mock embedding service to fix failed embeddings
        def mock_embed_chunks(chunk_list):
            for chunk in chunk_list:
                chunk.embedding = [0.3] * 768
            return chunk_list
        
        mock_embedding_service.embed_chunks.side_effect = mock_embed_chunks
        
        result = await embedding_processor.reprocess_failed_embeddings(chunks)
        
        # Should reprocess 2 chunks (wrong dimension + no embedding)
        mock_embedding_service.embed_chunks.assert_called_once()
        call_args = mock_embedding_service.embed_chunks.call_args[0][0]
        assert len(call_args) == 2


class TestEmbeddingFactory:
    """Test the factory function."""
    
    @patch('src.offline_pipeline.embeddings.GoogleAIEmbeddingService')
    def test_create_embedding_processor(self, mock_service_class):
        """Test creating embedding processor with factory function."""
        processor = create_embedding_processor()
        
        assert isinstance(processor, EmbeddingProcessor)
        mock_service_class.assert_called_once()


@pytest.mark.asyncio
class TestEmbeddingIntegration:
    """Integration tests for embedding service (requires API key)."""
    
    @pytest.mark.integration
    async def test_real_embedding_generation(self):
        """Test real embedding generation with Google AI."""
        # Skip if no API key available
        try:
            from src.shared.config import get_settings
            settings = get_settings()
            
            if not settings.google_api_key or settings.google_api_key == "test_google_key":
                pytest.skip("Google API key not configured")
            
            service = GoogleAIEmbeddingService()
            
            # Test single embedding
            embedding = await service.generate_single_embedding("This is a test legal document.")
            assert isinstance(embedding, list)
            assert len(embedding) == 768  # Expected dimension
            assert all(isinstance(x, float) for x in embedding)
            
            # Test batch embeddings
            texts = ["Legal document 1", "Legal document 2"]
            embeddings = await service.generate_embeddings(texts)
            assert len(embeddings) == 2
            assert all(len(emb) == 768 for emb in embeddings)
            
        except Exception as e:
            pytest.skip(f"Google AI API not available: {e}")
