"""Integration tests for the complete offline pipeline."""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch

from src.offline_pipeline.pipeline import OfflinePipeline, PipelineMode
from src.offline_pipeline.source_manager import DataSourceManager
from src.shared.models import DataSource, SourceType, ProcessingStatus


@pytest.mark.integration
class TestOfflinePipelineIntegration:
    """Integration tests for the complete offline pipeline."""
    
    @pytest.fixture
    async def temp_data_dir(self):
        """Create temporary data directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_settings(self, temp_data_dir):
        """Mock settings with temporary directories."""
        with patch('src.offline_pipeline.pipeline.get_settings') as mock:
            settings = mock.return_value
            settings.data_dir = temp_data_dir
            settings.crawled_data_dir = f"{temp_data_dir}/crawled"
            settings.processed_data_dir = f"{temp_data_dir}/processed"
            settings.indexes_dir = f"{temp_data_dir}/indexes"
            settings.sources_config = f"{temp_data_dir}/sources.yaml"
            settings.milvus_host = "localhost"
            settings.milvus_port = 19530
            settings.milvus_collection_name = "test_collection"
            settings.redis_host = "localhost"
            settings.redis_port = 6379
            settings.redis_db = 0
            settings.google_api_key = "test_key"
            settings.cohere_api_key = "test_key"
            settings.embedding_dimension = 768
            yield settings
    
    @pytest.fixture
    async def sample_sources_config(self, mock_settings):
        """Create sample sources configuration file."""
        config_content = """
websites:
  - name: "Test Legal Site"
    url: "https://httpbin.org/html"
    crawl_depth: 1
    enabled: true

local_pdfs:
  - name: "Test PDF"
    path: "/tmp/test.pdf"
    enabled: false

crawl_settings:
  delay_between_requests: 0.1
  max_concurrent_requests: 2
  timeout: 10

processing_settings:
  chunk_size: 500
  chunk_overlap: 100
"""
        
        config_path = Path(mock_settings.sources_config)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        config_path.write_text(config_content)
        
        return config_path
    
    @pytest.fixture
    async def pipeline(self, mock_settings, sample_sources_config):
        """Create pipeline instance for testing."""
        # Mock external services to avoid real connections
        with patch('src.offline_pipeline.storage.vector_store.MilvusVectorStore') as mock_vector:
            with patch('src.offline_pipeline.storage.metadata_store.MetadataStore') as mock_metadata:
                # Mock vector store
                mock_vector_instance = mock_vector.return_value
                mock_vector_instance.initialize = asyncio.coroutine(lambda: None)()
                mock_vector_instance.get_collection_stats = asyncio.coroutine(
                    lambda: {"total_entities": 0, "collection_name": "test"}
                )()
                
                # Mock metadata store
                mock_metadata_instance = mock_metadata.return_value
                mock_metadata_instance.initialize = asyncio.coroutine(lambda: None)()
                mock_metadata_instance.get_processing_stats = asyncio.coroutine(
                    lambda: {"total_sources": 0}
                )()
                
                pipeline = OfflinePipeline()
                await pipeline.initialize()
                yield pipeline
    
    async def test_pipeline_initialization(self, pipeline):
        """Test pipeline initialization."""
        assert pipeline.source_manager is not None
        assert pipeline.metadata_store is not None
        assert pipeline.is_running is False
        assert pipeline.stats["total_sources"] == 0
    
    async def test_pipeline_validation_mode(self, pipeline):
        """Test pipeline in validation mode."""
        result = await pipeline.run_pipeline(mode=PipelineMode.VALIDATE)
        
        assert "pipeline_valid" in result
        assert "source_validation" in result
        assert "configuration_issues" in result
        assert "recommendations" in result
    
    async def test_get_sources_for_mode_full(self, pipeline):
        """Test getting sources for full mode."""
        # Mock source manager to return test sources
        test_sources = [
            DataSource(
                id="test_1",
                name="Test Source 1",
                source_type=SourceType.WEBSITE,
                url="https://example.com",
                enabled=True
            ),
            DataSource(
                id="test_2",
                name="Test Source 2",
                source_type=SourceType.PDF,
                path="/tmp/test.pdf",
                enabled=False
            )
        ]
        
        with patch.object(pipeline.source_manager, 'get_all_sources', return_value=test_sources):
            sources = await pipeline._get_sources_for_mode(PipelineMode.FULL, None)
        
        # Should only return enabled sources
        assert len(sources) == 1
        assert sources[0].enabled is True
    
    async def test_get_sources_for_mode_specific_ids(self, pipeline):
        """Test getting specific sources by ID."""
        test_source = DataSource(
            id="test_1",
            name="Test Source 1",
            source_type=SourceType.WEBSITE,
            url="https://example.com",
            enabled=True
        )
        
        with patch.object(pipeline.source_manager, 'get_source', return_value=test_source):
            sources = await pipeline._get_sources_for_mode(PipelineMode.FULL, ["test_1"])
        
        assert len(sources) == 1
        assert sources[0].id == "test_1"
    
    async def test_pipeline_status(self, pipeline):
        """Test getting pipeline status."""
        status = await pipeline.get_pipeline_status()
        
        assert "is_running" in status
        assert "current_task" in status
        assert "stats" in status
        assert status["is_running"] is False
    
    async def test_validate_configuration(self, pipeline):
        """Test configuration validation."""
        issues = await pipeline._validate_configuration()
        
        # Should have issues with test API keys
        assert len(issues) >= 2  # Google and Cohere API keys
        assert any("Google API key" in issue for issue in issues)
        assert any("Cohere API key" in issue for issue in issues)
    
    async def test_validate_dependencies_mocked(self, pipeline):
        """Test dependency validation with mocked services."""
        # Mock successful connections
        with patch('src.offline_pipeline.storage.vector_store.MilvusVectorStore') as mock_vector:
            with patch('redis.Redis') as mock_redis:
                mock_vector_instance = mock_vector.return_value
                mock_vector_instance.initialize = asyncio.coroutine(lambda: None)()
                mock_vector_instance.close = asyncio.coroutine(lambda: None)()
                
                mock_redis_instance = mock_redis.return_value
                mock_redis_instance.ping.return_value = True
                
                issues = await pipeline._validate_dependencies()
        
        assert len(issues) == 0  # No issues with mocked services
    
    async def test_generate_recommendations(self, pipeline):
        """Test recommendation generation."""
        source_validation = {
            "invalid_sources": [{"source_name": "Bad Source", "issues": ["No URL"]}],
            "enabled_sources": 0
        }
        config_issues = ["Missing API key"]
        dependency_issues = ["Redis connection failed"]
        
        recommendations = pipeline._generate_recommendations(
            source_validation, config_issues, dependency_issues
        )
        
        assert len(recommendations) >= 3
        assert any("API keys" in rec for rec in recommendations)
        assert any("Redis" in rec for rec in recommendations)
        assert any("source configurations" in rec for rec in recommendations)
        assert any("Enable at least one" in rec for rec in recommendations)
    
    async def test_pipeline_stats(self, pipeline):
        """Test pipeline statistics tracking."""
        # Simulate some processing
        pipeline.stats["total_sources"] = 5
        pipeline.stats["processed_sources"] = 3
        pipeline.stats["failed_sources"] = 2
        pipeline.stats["total_chunks"] = 150
        pipeline.stats["errors"] = ["Error 1", "Error 2"]
        
        stats = pipeline._get_final_stats()
        
        assert stats["success"] is False  # Has failed sources
        assert stats["total_sources"] == 5
        assert stats["processed_sources"] == 3
        assert stats["failed_sources"] == 2
        assert stats["total_chunks"] == 150
        assert len(stats["errors"]) == 2


@pytest.mark.integration
class TestSourceManagerIntegration:
    """Integration tests for source manager."""
    
    @pytest.fixture
    async def temp_data_dir(self):
        """Create temporary data directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_settings(self, temp_data_dir):
        """Mock settings for source manager."""
        with patch('src.offline_pipeline.source_manager.get_settings') as mock:
            settings = mock.return_value
            settings.data_dir = temp_data_dir
            settings.sources_config = f"{temp_data_dir}/sources.yaml"
            yield settings
    
    @pytest.fixture
    async def sources_config(self, mock_settings):
        """Create test sources configuration."""
        config_content = """
websites:
  - name: "Legal Site 1"
    url: "https://example.com/legal"
    crawl_depth: 2
    enabled: true
    metadata:
      category: "legislation"

  - name: "Legal Site 2"
    url: "https://example.com/cases"
    crawl_depth: 1
    enabled: false

local_pdfs:
  - name: "Legal Commentary"
    path: "/tmp/commentary.pdf"
    enabled: true
    metadata:
      author: "Legal Expert"
"""
        
        config_path = Path(mock_settings.sources_config)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        config_path.write_text(config_content)
        
        return config_path
    
    @pytest.fixture
    async def source_manager(self, mock_settings, sources_config):
        """Create source manager instance."""
        with patch('src.offline_pipeline.storage.metadata_store.MetadataStore') as mock_store:
            # Mock metadata store
            mock_store_instance = mock_store.return_value
            mock_store_instance.initialize = asyncio.coroutine(lambda: None)()
            mock_store_instance.store_data_source = asyncio.coroutine(lambda x: True)()
            mock_store_instance.get_data_source = asyncio.coroutine(lambda x: None)()
            mock_store_instance.get_all_data_sources = asyncio.coroutine(lambda x=False: [])()
            mock_store_instance.get_processing_stats = asyncio.coroutine(
                lambda: {"total_sources": 0}
            )()
            
            manager = DataSourceManager()
            await manager.initialize()
            yield manager
    
    async def test_load_sources_from_config(self, source_manager):
        """Test loading sources from configuration file."""
        sources = await source_manager.load_sources_from_config()
        
        assert len(sources) == 3  # 2 websites + 1 PDF
        
        # Check website sources
        website_sources = [s for s in sources if s.source_type == SourceType.WEBSITE]
        assert len(website_sources) == 2
        
        enabled_website = next(s for s in website_sources if s.enabled)
        assert enabled_website.name == "Legal Site 1"
        assert enabled_website.url == "https://example.com/legal"
        assert enabled_website.crawl_depth == 2
        assert enabled_website.metadata["category"] == "legislation"
        
        # Check PDF source
        pdf_sources = [s for s in sources if s.source_type == SourceType.PDF]
        assert len(pdf_sources) == 1
        assert pdf_sources[0].name == "Legal Commentary"
        assert pdf_sources[0].path == "/tmp/commentary.pdf"
        assert pdf_sources[0].metadata["author"] == "Legal Expert"
    
    async def test_validate_sources(self, source_manager):
        """Test source validation."""
        # Mock sources with various issues
        with patch.object(source_manager, 'get_all_sources') as mock_get_sources:
            mock_get_sources.return_value = [
                DataSource(
                    id="valid_1",
                    name="Valid Website",
                    source_type=SourceType.WEBSITE,
                    url="https://example.com",
                    enabled=True
                ),
                DataSource(
                    id="invalid_1",
                    name="Invalid Website",
                    source_type=SourceType.WEBSITE,
                    url="",  # Missing URL
                    enabled=True
                ),
                DataSource(
                    id="invalid_2",
                    name="Invalid PDF",
                    source_type=SourceType.PDF,
                    path="/nonexistent/file.pdf",  # File doesn't exist
                    enabled=True
                )
            ]
            
            validation = await source_manager.validate_sources()
        
        assert validation["total_sources"] == 3
        assert validation["enabled_sources"] == 3
        assert validation["valid_sources"] == 1
        assert len(validation["invalid_sources"]) == 2
    
    async def test_source_changed_detection(self, source_manager):
        """Test detection of source changes."""
        original = DataSource(
            name="Test Source",
            source_type=SourceType.WEBSITE,
            url="https://example.com",
            crawl_depth=2,
            enabled=True
        )
        
        # Same source
        same = DataSource(
            name="Test Source",
            source_type=SourceType.WEBSITE,
            url="https://example.com",
            crawl_depth=2,
            enabled=True
        )
        
        # Changed URL
        changed_url = DataSource(
            name="Test Source",
            source_type=SourceType.WEBSITE,
            url="https://different.com",
            crawl_depth=2,
            enabled=True
        )
        
        # Changed depth
        changed_depth = DataSource(
            name="Test Source",
            source_type=SourceType.WEBSITE,
            url="https://example.com",
            crawl_depth=3,
            enabled=True
        )
        
        assert source_manager._source_changed(original, same) is False
        assert source_manager._source_changed(original, changed_url) is True
        assert source_manager._source_changed(original, changed_depth) is True


@pytest.mark.integration
@pytest.mark.slow
class TestFullPipelineIntegration:
    """Full end-to-end pipeline integration tests."""
    
    @pytest.mark.skipif(
        True,  # Skip by default as it requires external services
        reason="Requires running Milvus, Redis, and API keys"
    )
    async def test_complete_pipeline_workflow(self):
        """Test complete pipeline workflow with real services."""
        # This test would require:
        # 1. Running Milvus instance
        # 2. Running Redis instance
        # 3. Valid API keys
        # 4. Internet connection for crawling
        
        pipeline = OfflinePipeline()
        await pipeline.initialize()
        
        # Run validation first
        validation_result = await pipeline.run_pipeline(mode=PipelineMode.VALIDATE)
        
        if not validation_result.get("pipeline_valid"):
            pytest.skip("Pipeline validation failed - external services not available")
        
        # Run incremental processing
        result = await pipeline.run_pipeline(mode=PipelineMode.INCREMENTAL)
        
        assert result["success"] is True or len(result["errors"]) == 0
        assert "total_sources" in result
        assert "processed_sources" in result
